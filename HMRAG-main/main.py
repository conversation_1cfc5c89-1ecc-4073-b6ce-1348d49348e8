# 导入必要的库
import os  # 用于文件和目录操作
import re  # 用于正则表达式操作
import json  # 用于JSON数据处理
import argparse  # 用于命令行参数解析
import random  # 用于随机数生成
from tqdm import tqdm  # 用于显示进度条
import sys  # 用于系统相关操作
from agents.multi_retrieval_agents import MRetrievalAgent  # 导入自定义的多重检索代理类

# 导入OpenAI相关库
import openai
from openai import OpenAI
from langchain_community.llms.ollama import Ollama  # 导入Ollama语言模型

# 设置代理环境变量
import os
os.environ["http_proxy"] = "http://127.0.0.1:11434"
os.environ["https_proxy"] = "http://127.0.0.1:11434"

def parse_args():
    """
    解析命令行参数
    返回: 解析后的参数对象
    """
    parser = argparse.ArgumentParser()
    # 数据相关参数
    parser.add_argument('--data_root', type=str)  # 数据根目录
    parser.add_argument('--image_root', type=str)  # 图像根目录
    parser.add_argument('--output_root', type=str)  # 输出根目录
    parser.add_argument('--caption_file', type=str)  # 图像描述文件
    parser.add_argument('--model', type=str, default='gpt3')  # 使用的模型
    parser.add_argument('--options', type=list, default=["A", "B", "C", "D", "E"])  # 选项列表
    
    # 用户选项
    parser.add_argument('--test_split', type=str, default='test', choices=['test', 'val', 'minival'])  # 测试集划分
    parser.add_argument('--prompt_format',  # 提示格式
                        type=str,
                        default='CQM-A',
                        choices=[
                            'CQM-A', 'CQM-LA', 'CQM-EA', 'CQM-LEA', 'CQM-ELA', 'CQM-AL', 'CQM-AE', 'CQM-ALE', 'QCM-A',
                            'QCM-LA', 'QCM-EA', 'QCM-LEA', 'QCM-ELA', 'QCM-AL', 'QCM-AE', 'QCM-ALE', 'QCML-A', 'QCME-A',
                            'QCMLE-A', 'QCLM-A', 'QCEM-A', 'QCLEM-A', 'QCML-AE'
                        ],
                        help='prompt format template')
    
    # 向量检索设置
    parser.add_argument('--working_dir', type=str)  # 工作目录
    parser.add_argument('--llm_model_name', type=str, default='qwen2.5:7b')  # LLM模型名称
    parser.add_argument('--mode', type=str, default='hybrid')  # 运行模式
    parser.add_argument('--serper_api_key', type=str)  # Serper API密钥
    parser.add_argument('--top_k', type=int, default=4)  # 检索的top-k结果
    
    # GPT设置
    parser.add_argument('--openai_key', type=str)  # OpenAI API密钥
    parser.add_argument('--engine', type=str, default='gpt-4o')  # 使用的引擎
    parser.add_argument('--temperature', type=float, default=0.0)  # 温度参数
    parser.add_argument('--max_tokens',  # 最大token数
                        type=int,
                        default=512,
                        help='The maximum number of tokens allowed for the generated answer.')

    args = parser.parse_args()
    return args

def load_data(args):
    """
    加载数据
    参数:
        args: 命令行参数对象
    返回:
        problems: 问题数据
        qids: 测试问题ID列表
        shot_qids: 示例问题ID列表
    """
    # 加载问题数据
    problems = json.load(open(os.path.join(args.data_root, 'problems.json')))
    pid_splits = json.load(open(os.path.join(args.data_root, 'pid_splits.json')))
    captions = json.load(open(args.caption_file))["captions"]

    # 为每个问题添加图像描述
    for qid in problems:
        problems[qid]['caption'] = captions[qid] if qid in captions else ""

    # 获取测试集问题ID
    qids = pid_splits['%s' % (args.test_split)]
    qids = qids[:args.test_number] if args.test_number > 0 else qids
    print(f"number of test problems: {len(qids)}\n")

    # 从训练集中选择示例
    shot_qids = args.shot_qids
    train_qids = pid_splits['train']
    if shot_qids == None:
        assert args.shot_number >= 0 and args.shot_number <= 32
        shot_qids = random.sample(train_qids, args.shot_number)  # 随机采样
    else:
        shot_qids = [str(qid) for qid in shot_qids]
        for qid in shot_qids:
            assert qid in train_qids  # 检查shot_qids是否有效
    print("training question ids for prompting: ", shot_qids, "\n")

    return problems, qids, shot_qids

def main():
    """
    主函数：执行整个推理过程
    """
    # 解析命令行参数
    args = parse_args()
    print('====Input Arguments====')
    print(json.dumps(vars(args), indent=2, sort_keys=False))

    # 设置随机种子
    random.seed(args.seed)

    # 加载数据
    problems, qids, shot_qids = load_data(args)

    # 设置输出文件路径
    result_file = args.output_root + '/' + args.label + '_' + args.test_split + '.json'
    if not os.path.exists(args.output_root):
        os.makedirs(args.output_root)

    # 初始化检索代理
    sum_agent = MRetrievalAgent(args)
    correct = 0
    results = {}
    outputs = {}

    failed = []
    # 遍历测试问题
    for i, qid in enumerate(qids):
        if args.debug and i > 10:  # 调试模式下只处理前10个问题
            break
        if args.test_number > 0 and i >= args.test_number:  # 如果设置了测试数量限制
            break

        problem = problems[qid]
        answer = problem['answer']
        
        # 使用代理进行预测
        final_ans, all_messages = sum_agent.predict(problems, shot_qids, qid)
        outputs[qid] = all_messages
        results[qid] = final_ans
        
        # 统计正确率
        if final_ans == answer:
            correct += 1
        else:
            failed.append(qid)
            
        # 定期保存结果
        if (i + 1) % args.save_every == 0:
            with open(result_file, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"Results saved to {result_file} after {i + 1} examples.")

    # 保存最终结果
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"Results saved to {result_file} after {len(qids)} examples.")
    print(f"Number of correct answers: {correct}/{len(qids)}")
    print(f"Accuracy: {correct / len(qids):.4f}")
    print(f"Failed question ids: {failed}")
    print(f"Number of failed questions: {len(failed)}")

if __name__ == "__main__":
    main()


